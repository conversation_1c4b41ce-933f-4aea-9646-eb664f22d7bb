.container {
    width: 90%;
    max-width: 600px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    border: 1px solid #bcbcbc;
    margin-top: 20px;
}
.inputContainer {
    flex: 1;
    border-right: 1px solid #afacac;
    input {
        outline: none;
        border: none;
        width: 100%;
        padding: 5px 5px 5px 0px;
    }
}
.selectContainer {
    width: 80px;
    select {
        border: none;
    }
    select:focus-visible {
        border: none;
        outline: none;
    }
}
.label {
    display: block;
}
.flexRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    padding: 10px 20px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}
.error {
    color: red;
    font-weight: bold;
    // text-align: center;
}
