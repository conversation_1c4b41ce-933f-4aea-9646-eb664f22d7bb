{"name": "clock", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0 --fix", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "axios": "^1.9.0", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "mathjs": "^14.5.2", "normalize.css": "^8.0.1", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.3.1", "react-router-dom": "^6.25.1", "react-toastify": "^11.0.5", "sass": "^1.77.8", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.5.3", "tw-animate-css": "^1.3.4", "vite": "^5.3.1"}}