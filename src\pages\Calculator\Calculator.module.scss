.container {
    width: min(100%, 500px);
    margin: 30px auto 0 auto;
    padding: 40px 30px 30px;
    border-radius: 20px;
    box-shadow:
        25px 25px 75px rgba(0, 0, 0, 0.25),
        10px 10px 70px rgba(0, 0, 0, 0.25),
        inset -5px -5px 15px rgba(0, 0, 0, 0.25),
        inset 5px 5px 15px rgba(0, 0, 0, 0.25);
}
.title {
    text-align: center;
    font-size: 1.5 rem;
    color: #fff;
    font-weight: 700;
    margin-bottom: 20px;
}
.numberContainer {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 1rem;
}

.input {
    height: 100px;
    width: 100%;
    border: none;
    outline: none;
    background-color: #a7af7c;
    margin-bottom: 10px;
    border-radius: 10px;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.75);
    text-align: right;
    padding: 10px;
    font-size: 2em;
    margin-bottom: 20px;
    color: #fff;
}

body[data-theme='light'] {
    .title {
        color: #5e6f88;
    }
    .input {
        color: #000;
    }
}
