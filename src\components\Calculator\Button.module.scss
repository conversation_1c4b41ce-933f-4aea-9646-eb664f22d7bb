.btnContainer {
    color: #fff;
    position: relative;
    place-items: center;
    height: 80px;
    background: linear-gradient(180deg, #2f2f2f, #3f3f3f);
    box-shadow:
        inset -8px 0 8px rgba(0, 0, 0, 0.15),
        inset 0 -8px 8px rgba(0, 0, 0, 0.25),
        0 0 0 2px rgba(0, 0, 0, 0.75),
        10px 20px 25px rgba(0, 0, 0, 0.4);
    user-select: none;
    cursor: pointer;
    font-weight: 400;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.btnContainer:active {
    filter: brightness(1.5);
}
.btnContainer::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    bottom: 8px;
    right: 8px;
    border-radius: 10px;
    background: linear-gradient(90deg, #2d2d2d, #4d4d4d);
    box-shadow:
        -5px -5px 15px rgba(0, 0, 0, 0.1),
        10px 5px 10px rgba(0, 0, 0, 0.15);
    border-left: 1px solid #0004;
    border-bottom: 1px solid #0004;
    border-top: 1px solid #0009;
}
.btnContainer h2 {
    position: relative;
    font-style: normal;
    font-size: 1.5em;
    text-transform: uppercase;
}

.clear {
    background: #f00;
}
.clear::before {
    background: linear-gradient(90deg, #d20000, #ffffff5c);
    border-left: 1px solid #fff4;
    border-bottom: 1px solid #fff4;
    border-top: 1px solid #fff4;
}
.plus {
    grid-column: 4/5;
    grid-row: 3 / span 2;
    height: 100%;
}
.equal {
    background: #2196f3;
}
.equal::before {
    background: linear-gradient(90deg, #1479c9, #ffffff5c);
    border-left: 1px solid #fff4;
    border-bottom: 1px solid #fff4;
    border-top: 1px solid #fff4;
}
