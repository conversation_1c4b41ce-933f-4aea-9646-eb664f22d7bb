.heading {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.heading img {
    width: 50px;
    object-fit: cover;
}

.flexRow {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.time {
    font-weight: 500;
    color: #5e6f88;
}

/* Styles cho bảng */
.tableContainer {
    margin-top: 1.5rem;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    margin-bottom: 20px;
}

.table {
    overflow-x: scroll;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.95rem;
    border: 1px solid #e0e7ff;
    border-radius: 8px;
}

.table th {
    background-color: #f2f7ff;
    color: #394e6a;
    font-weight: 600;
    text-align: left;
    padding: 1rem;
    border-bottom: 2px solid #e0e7ff;
    border-right: 1px solid #e0e7ff;
    min-width: 130px;
}

.table th:last-child {
    border-right: none;
}

.table td {
    min-width: 130px;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e0e7ff;
    border-right: 1px solid #e0e7ff;
}

.table td:last-child {
    border-right: none;
}

.table tr:last-child td {
    border-bottom: none;
}

.table tbody tr:hover {
    background-color: #f8faff;
}

.brandInfo {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.brandName {
    font-weight: 600;
    color: #394e6a;
}

.updateTime {
    font-size: 0.8rem;
    color: #8896ab;
}

.buyValue {
    color: #16a34a;
    font-weight: 500;
}

.sellValue {
    color: #dc2626;
    font-weight: 500;
}

/* Dark mode */
body[data-theme='dark'] {
    .table {
        border: 1px solid #3c4473;
    }

    .table th {
        background-color: #2d2f36;
        color: #c8cbd0;
        border-bottom: 2px solid #3c4473;
        border-right: 1px solid #3c4473;
    }

    .table th:last-child {
        border-right: none;
    }

    .table td {
        border-bottom: 1px solid #3c4473;
        border-right: 1px solid #3c4473;
    }

    .table td:last-child {
        border-right: none;
    }
}

/* Nếu muốn cột đầu tiên có độ rộng cố định */
.table {
    table-layout: fixed; /* Thêm dòng này nếu muốn cột có độ rộng cố định */
}

/* Hoặc nếu muốn cột đầu tiên có độ rộng tương đối */
.table th:first-child,
.table td:first-child {
    width: 25%; /* Độ rộng tương đối so với bảng */
}

/* Responsive */
@media (max-width: 768px) {
    .table th:first-child,
    .table td:first-child {
        width: 120px; /* Giảm độ rộng trên màn hình nhỏ */
        min-width: 100px;
    }
}
@media (min-width: 992px) {
    .table {
        width: 100%;
    }
}

//Loading component
.loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loader {
    width: 50px;
    height: 50px;
    border: 3px solid #1846e0;
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
