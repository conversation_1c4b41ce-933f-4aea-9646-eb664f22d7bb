.content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #eef1f8;
    border-radius: 10px;
    .hour {
        font-size: 20px;
        color: #80828e;
    }

    .temperature {
        font-size: 20px;
        font-weight: bold;
    }
    .icon {
        width: 63px;
        height: 63px;
        max-width: none;
    }
}
body[data-theme='dark'] {
    .content {
        background-color: transparent;
    }
    .hour {
        color: #fff;
    }
}
